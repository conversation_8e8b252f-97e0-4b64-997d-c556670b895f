<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>浮光壁垒 - 专业互联网软件开发企业</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        line-height: 1.6;
        color: #fff;
        background: linear-gradient(
          45deg,
          #000000 0%,
          #1a0033 12.5%,
          #330066 25%,
          #000066 37.5%,
          #003366 50%,
          #006633 62.5%,
          #336600 75%,
          #663300 87.5%,
          #000000 100%
        );
        background-size: 400% 400%;
        animation: gradientShift 8s ease infinite;
      }

      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* 导航栏 */
      .navbar {
        background: linear-gradient(
          90deg,
          rgba(0, 0, 0, 0.9) 0%,
          rgba(26, 0, 51, 0.9) 25%,
          rgba(51, 0, 102, 0.9) 50%,
          rgba(0, 51, 102, 0.9) 75%,
          rgba(0, 0, 0, 0.9) 100%
        );
        backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        box-shadow: 0 2px 20px rgba(255, 0, 255, 0.3);
        border-bottom: 2px solid;
        border-image: linear-gradient(
            90deg,
            #ff0080,
            #8000ff,
            #0080ff,
            #00ff80,
            #ff8000
          )
          1;
      }

      .nav-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
      }

      .logo {
        font-size: 1.8rem;
        font-weight: bold;
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000,
          #ff0080
        );
        background-size: 300% 300%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: rainbowText 3s ease infinite;
        text-decoration: none;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
      }

      @keyframes rainbowText {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 2rem;
      }

      .nav-links a {
        text-decoration: none;
        color: #fff;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
      }

      .nav-links a:hover {
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000
        );
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: rainbowText 1s ease infinite;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
      }

      /* 主页面 - 赛博朋克主题 */
      .hero {
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        position: relative;
        background: linear-gradient(
          135deg,
          #0a0a0a 0%,
          #1a0033 25%,
          #000066 50%,
          #330066 75%,
          #0a0a0a 100%
        );
        overflow: hidden;
      }

      .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 80%,
            rgba(0, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 0, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(0, 255, 128, 0.05) 0%,
            transparent 50%
          );
        animation: cyberGlow 8s ease-in-out infinite alternate;
      }

      @keyframes cyberGlow {
        0% {
          opacity: 0.3;
          transform: scale(1);
        }
        100% {
          opacity: 0.7;
          transform: scale(1.1);
        }
      }

      .hero::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          90deg,
          transparent,
          transparent 2px,
          rgba(0, 255, 255, 0.03) 2px,
          rgba(0, 255, 255, 0.03) 4px
        );
        animation: scanlines 2s linear infinite;
      }

      @keyframes scanlines {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      /* 网格背景效果 */
      .hero::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: linear-gradient(
            rgba(0, 255, 255, 0.1) 1px,
            transparent 1px
          ),
          linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        animation: gridMove 20s linear infinite;
        z-index: 1;
      }

      @keyframes gridMove {
        0% {
          transform: translate(0, 0);
        }
        100% {
          transform: translate(50px, 50px);
        }
      }

      /* 数字雨效果 */
      .digital-rain {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 2;
        pointer-events: none;
      }

      .rain-column {
        position: absolute;
        top: -100px;
        font-family: "Courier New", monospace;
        font-size: 14px;
        color: #00ff00;
        opacity: 0.3;
        animation: digitalFall 8s linear infinite;
        text-shadow: 0 0 5px #00ff00;
      }

      .rain-column:nth-child(1) {
        left: 10%;
        animation-delay: 0s;
        animation-duration: 6s;
      }

      .rain-column:nth-child(2) {
        left: 30%;
        animation-delay: 2s;
        animation-duration: 8s;
      }

      .rain-column:nth-child(3) {
        left: 50%;
        animation-delay: 4s;
        animation-duration: 7s;
      }

      .rain-column:nth-child(4) {
        left: 70%;
        animation-delay: 1s;
        animation-duration: 9s;
      }

      .rain-column:nth-child(5) {
        left: 90%;
        animation-delay: 3s;
        animation-duration: 5s;
      }

      @keyframes digitalFall {
        0% {
          transform: translateY(-100px);
          opacity: 0;
        }
        10% {
          opacity: 0.3;
        }
        90% {
          opacity: 0.3;
        }
        100% {
          transform: translateY(100vh);
          opacity: 0;
        }
      }

      /* 鼠标跟随光标效果 */
      .cursor-glow {
        position: fixed;
        width: 20px;
        height: 20px;
        background: radial-gradient(
          circle,
          rgba(0, 255, 255, 0.8) 0%,
          transparent 70%
        );
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        mix-blend-mode: screen;
        transition: transform 0.1s ease;
      }

      .cursor-trail {
        position: fixed;
        width: 6px;
        height: 6px;
        background: rgba(255, 0, 255, 0.6);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        transition: all 0.3s ease;
      }

      /* 点击波纹效果 */
      .click-ripple {
        position: fixed;
        border: 2px solid #00ffff;
        border-radius: 50%;
        pointer-events: none;
        z-index: 9997;
        animation: rippleEffect 0.6s ease-out forwards;
      }

      @keyframes rippleEffect {
        0% {
          width: 0;
          height: 0;
          opacity: 1;
        }
        100% {
          width: 100px;
          height: 100px;
          opacity: 0;
        }
      }

      /* 鼠标悬停时的全局效果 */
      .hover-effect {
        transition: all 0.3s ease;
      }

      .hover-effect:hover {
        transform: translateY(-2px);
        filter: drop-shadow(0 5px 15px rgba(0, 255, 255, 0.3));
      }

      /* 鼠标移动时的背景响应 */
      .hero.mouse-active::before {
        animation: cyberGlow 2s ease-in-out infinite alternate,
          mouseGlow 0.1s ease;
      }

      @keyframes mouseGlow {
        0% {
          opacity: 0.3;
        }
        100% {
          opacity: 0.8;
        }
      }

      /* 鼠标悬停时数字雨变色 */
      .hero.mouse-active .rain-column {
        color: #00ffff;
        text-shadow: 0 0 10px #00ffff;
        animation-duration: 3s;
      }

      /* 鼠标悬停时网格增强 */
      .hero.mouse-active::after {
        background-image: linear-gradient(
            rgba(0, 255, 255, 0.2) 1px,
            transparent 1px
          ),
          linear-gradient(90deg, rgba(0, 255, 255, 0.2) 1px, transparent 1px);
        animation-duration: 10s;
      }

      /* 隐藏默认光标 */
      body {
        cursor: none;
      }

      /* 在可点击元素上显示指针 */
      a,
      button,
      .service-card,
      .nav-links a {
        cursor: none;
      }

      /* 悬停时光标变大 */
      .cursor-glow.hover {
        width: 30px;
        height: 30px;
        background: radial-gradient(
          circle,
          rgba(255, 0, 255, 0.8) 0%,
          transparent 70%
        );
      }

      .hero-content {
        position: relative;
        z-index: 10;
      }

      .hero-content h1 {
        font-size: 4rem;
        margin-bottom: 1rem;
        font-family: "Courier New", monospace;
        font-weight: bold;
        background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
        background-size: 400% 400%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: cyberText 3s ease infinite;
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.5),
          0 0 10px rgba(0, 255, 255, 0.3), 0 0 15px rgba(0, 255, 255, 0.2);
        filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.4));
        position: relative;
      }

      .hero-content h1::before {
        content: "> ";
        color: #00ff00;
        font-size: 0.8em;
        animation: blink 1s infinite;
      }

      .hero-content h1::after {
        content: "_";
        color: #00ff00;
        animation: blink 1s infinite;
        margin-left: 5px;
      }

      @keyframes cyberText {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes blink {
        0%,
        50% {
          opacity: 1;
        }
        51%,
        100% {
          opacity: 0;
        }
      }

      .hero-content p {
        font-size: 1.4rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        font-family: "Courier New", monospace;
        color: #00ffff;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        animation: textGlow 2s ease-in-out infinite alternate;
      }

      @keyframes textGlow {
        0% {
          text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        100% {
          text-shadow: 0 0 20px rgba(0, 255, 255, 0.8),
            0 0 30px rgba(0, 255, 255, 0.3);
        }
      }

      .cta-button {
        display: inline-block;
        padding: 15px 30px;
        background: linear-gradient(
          45deg,
          rgba(0, 255, 255, 0.1),
          rgba(255, 0, 255, 0.1)
        );
        color: #00ffff;
        text-decoration: none;
        border-radius: 0;
        border: 2px solid #00ffff;
        font-weight: bold;
        font-family: "Courier New", monospace;
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.3),
          inset 0 0 10px rgba(0, 255, 255, 0.1);
      }

      .cta-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(0, 255, 255, 0.3),
          transparent
        );
        transition: left 0.5s ease;
      }

      .cta-button:hover::before {
        left: 100%;
      }

      .cta-button:hover {
        background: linear-gradient(
          45deg,
          rgba(0, 255, 255, 0.2),
          rgba(255, 0, 255, 0.2)
        );
        color: #ffffff;
        border-color: #ff00ff;
        box-shadow: 0 0 20px rgba(255, 0, 255, 0.5),
          inset 0 0 20px rgba(255, 0, 255, 0.1);
        text-shadow: 0 0 10px rgba(255, 0, 255, 0.8);
      }

      /* 内容区域 */
      .section {
        padding: 80px 0;
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.95) 0%,
          rgba(13, 0, 26, 0.95) 25%,
          rgba(26, 0, 51, 0.95) 50%,
          rgba(13, 26, 51, 0.95) 75%,
          rgba(0, 0, 0, 0.95) 100%
        );
        position: relative;
        overflow: hidden;
      }

      .section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 0, 128, 0.05),
          rgba(128, 0, 255, 0.05),
          rgba(0, 128, 255, 0.05),
          rgba(0, 255, 128, 0.05),
          rgba(255, 128, 0, 0.05)
        );
        background-size: 600% 600%;
        animation: gradientShift 15s ease infinite;
        z-index: 0;
      }

      .section-title {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000,
          #ff0080
        );
        background-size: 300% 300%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: rainbowText 4s ease infinite;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
        position: relative;
        z-index: 1;
      }

      .services {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
        position: relative;
        z-index: 1;
      }

      .service-card {
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.8) 0%,
          rgba(26, 0, 51, 0.8) 25%,
          rgba(51, 0, 102, 0.8) 50%,
          rgba(0, 51, 102, 0.8) 75%,
          rgba(0, 0, 0, 0.8) 100%
        );
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        background-clip: padding-box;
        position: relative;
        overflow: hidden;
      }

      .service-card::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000,
          #ff0080
        );
        background-size: 300% 300%;
        border-radius: 17px;
        z-index: -1;
        animation: rainbowBorder 4s ease infinite;
      }

      @keyframes rainbowBorder {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .service-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(255, 0, 255, 0.3);
      }

      .service-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000
        );
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: rainbowText 2s ease infinite;
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
      }

      .service-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        background: linear-gradient(
          45deg,
          #ff0080,
          #8000ff,
          #0080ff,
          #00ff80,
          #ff8000
        );
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: rainbowText 3s ease infinite;
      }

      /* 关于我们 */
      .about {
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.9) 0%,
          rgba(26, 0, 51, 0.9) 25%,
          rgba(51, 0, 102, 0.9) 50%,
          rgba(0, 51, 102, 0.9) 75%,
          rgba(0, 0, 0, 0.9) 100%
        );
        position: relative;
        overflow: hidden;
      }

      .about::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 0, 128, 0.1),
          rgba(128, 0, 255, 0.1),
          rgba(0, 128, 255, 0.1),
          rgba(0, 255, 128, 0.1),
          rgba(255, 128, 0, 0.1)
        );
        background-size: 400% 400%;
        animation: gradientShift 10s ease infinite;
        z-index: 0;
      }

      .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
        position: relative;
        z-index: 1;
      }

      .about-text {
        font-size: 1.1rem;
        line-height: 1.8;
      }

      .about-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .stat-item {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
      }

      /* 页脚 */
      .footer {
        background: #2c3e50;
        color: white;
        padding: 3rem 0;
        text-align: center;
      }

      .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .footer-section h3 {
        margin-bottom: 1rem;
        color: #667eea;
      }

      .footer-section p,
      .footer-section a {
        color: #bdc3c7;
        text-decoration: none;
        line-height: 1.8;
      }

      .footer-section a:hover {
        color: white;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .hero-content h1 {
          font-size: 2.5rem;
        }

        .nav-links {
          display: none;
        }

        .about-content {
          grid-template-columns: 1fr;
        }

        .services {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <!-- 鼠标跟随效果 -->
    <div class="cursor-glow"></div>
    <div class="cursor-trail"></div>

    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="container">
        <div class="nav-content">
          <a href="#" class="logo">浮光壁垒</a>
          <ul class="nav-links">
            <li><a href="#home">首页</a></li>
            <li><a href="#services">服务</a></li>
            <li><a href="#about">关于我们</a></li>
            <li><a href="#contact">联系我们</a></li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- 主页面 -->
    <section id="home" class="hero">
      <!-- 数字雨效果 -->
      <div class="digital-rain">
        <div class="rain-column">01001000</div>
        <div class="rain-column">11010110</div>
        <div class="rain-column">00110101</div>
        <div class="rain-column">10101010</div>
        <div class="rain-column">01110011</div>
      </div>

      <div class="hero-content">
        <h1>浮光壁垒</h1>
        <p>// 连接未来 | 构建数字边界 | 突破技术壁垒</p>
        <p>CYBER_DEVELOPMENT.EXE | STATUS: ONLINE | READY_FOR_MISSION</p>
        <a href="#services" class="cta-button">ENTER_SYSTEM</a>
      </div>
    </section>

    <!-- 服务介绍 -->
    <section id="services" class="section">
      <div class="container">
        <h2 class="section-title">我们的服务</h2>
        <div class="services">
          <div class="service-card hover-effect">
            <div class="service-icon">💻</div>
            <h3>Web应用开发</h3>
            <p>
              专业的前端和后端开发团队，为您打造高性能、用户友好的Web应用程序
            </p>
          </div>
          <div class="service-card hover-effect">
            <div class="service-icon">📱</div>
            <h3>移动应用开发</h3>
            <p>
              iOS和Android原生应用开发，以及跨平台解决方案，满足您的移动端需求
            </p>
          </div>
          <div class="service-card hover-effect">
            <div class="service-icon">☁️</div>
            <h3>云服务解决方案</h3>
            <p>
              提供完整的云架构设计、部署和维护服务，确保您的应用稳定高效运行
            </p>
          </div>
          <div class="service-card hover-effect">
            <div class="service-icon">🔧</div>
            <h3>技术咨询</h3>
            <p>专业的技术顾问团队，为您的项目提供最佳的技术选型和架构建议</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="section about">
      <div class="container">
        <h2 class="section-title">关于浮光壁垒</h2>
        <div class="about-content">
          <div class="about-text">
            <p>
              浮光壁垒成立于2020年，是一家专注于互联网软件开发的创新型企业。我们致力于为客户提供高质量的软件解决方案，涵盖Web开发、移动应用开发、云服务等多个领域。
            </p>
            <p>
              我们的团队由经验丰富的开发工程师、设计师和项目经理组成，拥有深厚的技术功底和丰富的项目经验。我们始终坚持以客户需求为导向，以技术创新为驱动，为客户创造最大价值。
            </p>
          </div>
          <div class="about-stats">
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <p>成功项目</p>
            </div>
            <div class="stat-item">
              <div class="stat-number">30+</div>
              <p>合作客户</p>
            </div>
            <div class="stat-item">
              <div class="stat-number">4年</div>
              <p>行业经验</p>
            </div>
            <div class="stat-item">
              <div class="stat-number">15人</div>
              <p>专业团队</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer id="contact" class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>联系方式</h3>
            <p>电话：400-123-4567</p>
            <p>邮箱：<EMAIL></p>
            <p>地址：北京市朝阳区科技园区</p>
          </div>
          <div class="footer-section">
            <h3>服务领域</h3>
            <p>Web应用开发</p>
            <p>移动应用开发</p>
            <p>云服务解决方案</p>
            <p>技术咨询服务</p>
          </div>
          <div class="footer-section">
            <h3>关于我们</h3>
            <p>
              浮光壁垒专注于为客户提供专业的互联网软件开发服务，以技术创新为核心，为客户创造价值。
            </p>
          </div>
        </div>
        <p>&copy; 2024 浮光壁垒. 保留所有权利.</p>
      </div>
    </footer>

    <script>
      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute("href")).scrollIntoView({
            behavior: "smooth",
          });
        });
      });

      // 导航栏滚动效果
      window.addEventListener("scroll", function () {
        const navbar = document.querySelector(".navbar");
        if (window.scrollY > 50) {
          // 滚动时保持五彩斑斓的黑效果
          navbar.style.background =
            "linear-gradient(90deg, rgba(0,0,0,0.95) 0%, rgba(26,0,51,0.95) 25%, rgba(51,0,102,0.95) 50%, rgba(0,51,102,0.95) 75%, rgba(0,0,0,0.95) 100%)";
          navbar.style.boxShadow = "0 2px 20px rgba(255, 0, 255, 0.4)";
        } else {
          // 回到顶部时重置为原始样式
          navbar.style.background =
            "linear-gradient(90deg, rgba(0,0,0,0.9) 0%, rgba(26,0,51,0.9) 25%, rgba(51,0,102,0.9) 50%, rgba(0,51,102,0.9) 75%, rgba(0,0,0,0.9) 100%)";
          navbar.style.boxShadow = "0 2px 20px rgba(255, 0, 255, 0.3)";
        }
      });

      // 鼠标跟随光标效果
      const cursorGlow = document.querySelector(".cursor-glow");
      const cursorTrail = document.querySelector(".cursor-trail");
      const hero = document.querySelector(".hero");

      let mouseX = 0,
        mouseY = 0;
      let trailX = 0,
        trailY = 0;

      // 鼠标移动事件
      document.addEventListener("mousemove", function (e) {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // 更新光标位置
        cursorGlow.style.left = mouseX - 10 + "px";
        cursorGlow.style.top = mouseY - 10 + "px";

        // 检查是否在主页区域
        const heroRect = hero.getBoundingClientRect();
        if (mouseY >= heroRect.top && mouseY <= heroRect.bottom) {
          hero.classList.add("mouse-active");
          // 根据鼠标位置改变背景光效
          const x = ((mouseX - heroRect.left) / heroRect.width) * 100;
          const y = ((mouseY - heroRect.top) / heroRect.height) * 100;
          hero.style.setProperty("--mouse-x", x + "%");
          hero.style.setProperty("--mouse-y", y + "%");
        } else {
          hero.classList.remove("mouse-active");
        }
      });

      // 鼠标拖尾效果
      function updateTrail() {
        trailX += (mouseX - trailX) * 0.1;
        trailY += (mouseY - trailY) * 0.1;

        cursorTrail.style.left = trailX - 3 + "px";
        cursorTrail.style.top = trailY - 3 + "px";

        requestAnimationFrame(updateTrail);
      }
      updateTrail();

      // 点击波纹效果
      document.addEventListener("click", function (e) {
        const ripple = document.createElement("div");
        ripple.className = "click-ripple";
        ripple.style.left = e.clientX - 50 + "px";
        ripple.style.top = e.clientY - 50 + "px";

        document.body.appendChild(ripple);

        // 移除波纹元素
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });

      // 鼠标进入和离开页面的处理
      document.addEventListener("mouseenter", function () {
        cursorGlow.style.opacity = "1";
        cursorTrail.style.opacity = "1";
      });

      document.addEventListener("mouseleave", function () {
        cursorGlow.style.opacity = "0";
        cursorTrail.style.opacity = "0";
        hero.classList.remove("mouse-active");
      });

      // 可点击元素的悬停效果
      const clickableElements = document.querySelectorAll(
        "a, button, .service-card, .nav-links a"
      );

      clickableElements.forEach((element) => {
        element.addEventListener("mouseenter", function () {
          cursorGlow.classList.add("hover");
          cursorTrail.style.transform = "scale(1.5)";
        });

        element.addEventListener("mouseleave", function () {
          cursorGlow.classList.remove("hover");
          cursorTrail.style.transform = "scale(1)";
        });
      });

      // 键盘交互效果
      document.addEventListener("keydown", function (e) {
        if (e.key === "Enter" || e.key === " ") {
          // 创建键盘激活的波纹效果
          const ripple = document.createElement("div");
          ripple.className = "click-ripple";
          ripple.style.left = window.innerWidth / 2 - 50 + "px";
          ripple.style.top = window.innerHeight / 2 - 50 + "px";
          ripple.style.borderColor = "#00ff00";

          document.body.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        }
      });

      // 滚动时的光标效果
      window.addEventListener("scroll", function () {
        const scrollPercent =
          window.scrollY /
          (document.documentElement.scrollHeight - window.innerHeight);
        cursorGlow.style.filter = `hue-rotate(${scrollPercent * 360}deg)`;
      });
    </script>
  </body>
</html>
